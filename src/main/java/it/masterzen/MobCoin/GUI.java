package it.masterzen.MobCoin;

import com.google.common.base.CaseFormat;
import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.Keys.KeyList;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.commands.Main;
import it.masterzen.prestigemine.XMaterial;
import it.masterzen.MongoDB.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class GUI implements Listener {

    private it.masterzen.MobCoin.Main main;
    private it.masterzen.Keys.Main keyManager;
    private it.masterzen.Withdraw.Main withdrawManager;
    private final String prefix = "§e§lMOBCOINS §8»§7 ";

    public GUI(it.masterzen.MobCoin.Main main) {
        this.main = main;
        keyManager = main.mainClass.getKeysManager();
        withdrawManager = new it.masterzen.Withdraw.Main(main.mainClass);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            if (event.getView().getTitle().equalsIgnoreCase("§e§lMOBCOIN §f| §7Shop")) {
                event.setCancelled(true);
                if (event.getSlot() == 11) {
                    if (event.isLeftClick()) {
                        giveXP(player, false);
                    } else if (event.isRightClick()) {
                        giveXP(player, true);
                    }
                } else if (event.getSlot() == 15) {
                    if (event.isLeftClick()) {
                        giveKeys(player, false);
                    } else if (event.isRightClick()) {
                        giveKeys(player, true);
                    }
                } else if (event.getSlot() == 10) {
                    if (event.isLeftClick()) {
                        giveTokens(player, false);
                    } else if (event.isRightClick()) {
                        giveTokens(player, true);
                    }
                } else if (event.getSlot() == 12) {
                    if (event.isLeftClick()) {
                        giveMoney(player, false);
                    } else if (event.isRightClick()) {
                        giveMoney(player, true);
                    }
                } else if (event.getSlot() == 13) {
                    giveMoneyBooster(player);
                } else if (event.getSlot() == 14) {
                    giveTokenBooster(player);
                } /*else if (event.getSlot() == 22) {
                    giveStick(player);
                }*/ else if (event.getSlot() == 26) {
                    openStickUpgrades(player);
                    return;
                }
                openGUI(player);
            } else if (event.getView().getTitle().equalsIgnoreCase("§e§lMOBCOIN §f| §7Stick Upgrade")) {
                event.setCancelled(true);
                if (event.getSlot() == 11) {
                    upgradeStick(player, 2);
                } else if (event.getSlot() == 12) {
                    upgradeStick(player, 3);
                } else if (event.getSlot() == 14) {
                    upgradeStick(player, 4);
                } else if (event.getSlot() == 15) {
                    upgradeStick(player, 5);
                }
            }
        }
    }

    public void upgradeStick(Player player, int level) {
        int stickAvailable = 0;

        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.isSimilar(getStickItem(level - 1))) {
                stickAvailable = stickAvailable + item.getAmount();
            }
        }

        if (stickAvailable >= 4) {
            stickAvailable = 4;
            for (ItemStack item : player.getInventory().getContents()) {
                if (item != null && item.isSimilar(getStickItem(level - 1)) && stickAvailable > 0) {
                    if (item.getAmount() >= stickAvailable) {
                        item.setAmount(item.getAmount() - stickAvailable);
                        stickAvailable = 0;
                    } else {
                        stickAvailable = stickAvailable - item.getAmount();
                        main.mainClass.removeItemFromPlayer(player, item, null, false, true, 4);
                    }
                }
            }

            player.getInventory().addItem(getStickItem(level));
            player.sendMessage(prefix + "You upgraded your collector to level §a§l" + level);
        } else {
            player.sendMessage(prefix + "§cYou need 4 collectors to upgrade");
        }
    }

    public void giveStick(Player player) {
        long playerBalance = main.getMobCoins(player);
        if (playerBalance >= 15000) {
            int chance = ThreadLocalRandom.current().nextInt(100);
            if (chance <= 50) {
                ItemStack stick = new ItemStack(Material.STICK);
                ItemMeta meta = stick.getItemMeta();
                meta.setDisplayName("§9§lMOB §5§lCOIN §7- Collector");
                List<String> lore = new ArrayList<>();
                lore.add("");
                lore.add("§7Right click on a chest");
                lore.add("§7to collect all the MobCoins");
                meta.setLore(lore);

                stick.setItemMeta(meta);
                player.getInventory().addItem(stick);
            } else {
                player.sendMessage(prefix + "§cYou were unlucky. Try again");
            }
            main.removeMobCoins(player, 15000);
        } else {
            player.sendMessage(prefix + "§cYou don't have enough MobCoins");
        }
    }

    public void giveXP(Player player, boolean max) {
        long playerBalance = main.getMobCoins(player);
        if (playerBalance >= 1000) {
            if (max) {
                long totalXP = 0;
                int iterations = 0;

                while (playerBalance >= 1000) {
                    totalXP = totalXP + ThreadLocalRandom.current().nextInt(50000) + 50000; // 50k to 100k
                    playerBalance = playerBalance - 1000;
                    iterations++;
                }

                int maxIteraions = 1000;
                while (totalXP > 0 && maxIteraions > 0) {
                    if (totalXP > 1000000000) {
                        withdrawManager.giveItem(player, "XP", 1000000000);
                        totalXP = totalXP - 1000000000;
                    } else {
                        withdrawManager.giveItem(player, "XP", totalXP);
                        totalXP = 0;
                    }
                    maxIteraions--;
                }
                main.removeMobCoins(player, iterations * 1000L);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalXP));
            } else {
                int totalXP = ThreadLocalRandom.current().nextInt(50000) + 50000;
                withdrawManager.giveItem(player, "XP", totalXP);
                main.removeMobCoins(player, 1000);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalXP));
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough MobCoins");
        }
    }

    public void giveTokens(Player player, boolean max) {
        long playerBalance = main.getMobCoins(player);
        int cost = 1000;
        if (playerBalance >= cost) {
            if (max) {
                long totalTokens = 0;
                int iterations = 0;
                while (playerBalance >= cost) {
                    totalTokens = totalTokens + ThreadLocalRandom.current().nextInt(25000) + 25000; // 25k to 50k
                    playerBalance = playerBalance - cost;
                    iterations++;
                }
                withdrawManager.giveItem(player, "Tokens", totalTokens);
                main.removeMobCoins(player, iterations * (long) cost);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalTokens) + " §7Tokens");
            } else {
                int totalTokens = ThreadLocalRandom.current().nextInt(25000) + 25000;
                withdrawManager.giveItem(player, "Tokens", totalTokens);
                main.removeMobCoins(player, cost);
                player.sendMessage(prefix + "You received §a§l" + main.mainClass.newFormatNumber(totalTokens) + " §7Tokens");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough MobCoins");
        }
    }

    public void giveMoney(Player player, boolean max) {
        long playerBalance = main.getMobCoins(player);
        int cost = 1500;
        if (playerBalance >= cost) {
            if (max) {
                double totalMoney = 0;
                int iterations = 0;
                while (playerBalance >= cost) {
                    totalMoney = totalMoney + (250000000D + ThreadLocalRandom.current().nextInt(250000000)); // 250M to 500M
                    playerBalance = playerBalance - cost;
                    iterations++;
                }
                withdrawManager.giveItem(player, "Money", totalMoney);
                main.removeMobCoins(player, iterations * (long) cost);
                player.sendMessage(prefix + "You received a total of §a§l" + main.mainClass.newFormatNumber(totalMoney) + " §7Money");
            } else {
                double totalMoney = 250000000D + ThreadLocalRandom.current().nextInt(250000000);
                withdrawManager.giveItem(player, "Money", totalMoney);
                main.removeMobCoins(player, cost);
                player.sendMessage(prefix + "You received §a§l" + main.mainClass.newFormatNumber(totalMoney) + " §7Money");
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough MobCoins");
        }
    }

    public void giveMoneyBooster(Player player) {
        long cost = 5000;
        if (main.getMobCoins(player) >= cost) {
            PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            double current = (data.getKeysMoneyBooster() == null ? 0 : data.getKeysMoneyBooster());
            data.setKeysMoneyBooster(current + 5);
            main.mainClass.getMongoReader().setPlayerMoneyBoosterInMap(player, data.getKeysMoneyBooster());
            main.removeMobCoins(player, cost);
            player.sendMessage(prefix + "You received a §a§l5% §7Money Booster");
        } else {
            player.sendMessage(prefix + "§cYou don't have enough MobCoins");
        }
    }

    public void giveTokenBooster(Player player) {
        long cost = 5000;
        if (main.getMobCoins(player) >= cost) {
            PlayerData data = main.mainClass.getMongoReader().getPlayerData(player.getUniqueId());
            double current = (data.getKeysTokenBooster() == null ? 0 : data.getKeysTokenBooster());
            data.setKeysTokenBooster(current + 2.5);
            main.mainClass.getMongoReader().setPlayerTokenBoosterInMap(player, data.getKeysTokenBooster());
            main.removeMobCoins(player, cost);
            player.sendMessage(prefix + "You received a §a§l2.5% §7Token Booster");
        } else {
            player.sendMessage(prefix + "§cYou don't have enough MobCoins");
        }
    }

    public void giveKeys(Player player, boolean max) {
        long playerBalance = main.getMobCoins(player);
        if (playerBalance >= 2500) {
            if (max) {
                int iterations = 0;

                int totalDelta = 0;
                int totalBeta = 0;
                int totalGamma = 0;
                int totalToken = 0;
                //int totalRandom = 0;
                int totalOmega = 0;

                while (playerBalance >= 2500) {
                    int chance = ThreadLocalRandom.current().nextInt(15) + 1;

                    if (chance <= 1) {
                        int keys = ThreadLocalRandom.current().nextInt(2) + 1;
                        totalOmega = totalOmega + keys;
                    } else if (chance <= 3) {
                        int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                        totalToken = totalToken + keys;
                    } else if (chance <= 6) {
                        int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                        totalGamma = totalGamma + keys;
                    } else if (chance <= 10) {
                        int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                        totalBeta = totalBeta + keys;
                    } else {
                        int keys = ThreadLocalRandom.current().nextInt(8) + 1;
                        totalDelta = totalDelta + keys;
                    }

                    iterations++;
                    playerBalance = playerBalance - 2500;
                }

                if (totalDelta > 0) {
                    keyManager.giveKeys(player, "Delta", totalDelta, !player.hasPermission("keyfinder.remove"));
                    player.sendMessage(prefix + "You received §a§l" + totalDelta + " " + keyManager.getKeyName("Delta"));
                }
                if (totalBeta > 0) {
                    keyManager.giveKeys(player, "Beta", totalBeta, !player.hasPermission("keyfinder.remove"));
                    player.sendMessage(prefix + "You received §a§l" + totalBeta + " " + keyManager.getKeyName("Beta"));
                }
                if (totalGamma > 0) {
                    keyManager.giveKeys(player, "Gamma", totalGamma, !player.hasPermission("keyfinder.remove"));
                    player.sendMessage(prefix + "You received §a§l" + totalGamma + " " + keyManager.getKeyName("Gamma"));
                }
                if (totalToken > 0) {
                    keyManager.giveKeys(player, "Token", totalToken, !player.hasPermission("keyfinder.remove"));
                    player.sendMessage(prefix + "You received §a§l" + totalToken + " " + keyManager.getKeyName("Token"));
                }
                //if (totalRandom > 0) {
                //    keyManager.giveKeys(player, "Random", totalRandom, false);
                //    player.sendMessage(prefix + "You received §a§l" + totalRandom + " " + keyManager.getKeyName("Random"));
                //}
                if (totalOmega > 0) {
                    keyManager.giveKeys(player, "Omega", totalOmega, !player.hasPermission("keyfinder.remove"));
                    player.sendMessage(prefix + "You received §a§l" + totalOmega + " " + keyManager.getKeyName("Omega"));
                }
                main.removeMobCoins(player, iterations * 2500L);
            } else {
                int chance = ThreadLocalRandom.current().nextInt(100);

                /*if (chance <= 1) {
                    int keys = ThreadLocalRandom.current().nextInt(2) + 1;
                    keyManager.giveKeys(player, "Omega", keys, false);
                    player.sendMessage(prefix + "You received §a§l" + keys + " " + keyManager.getKeyName("Omega"));
                } else if (chance <= 25) {
                    int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                    keyManager.giveKeys(player, "Random", keys, false);
                    player.sendMessage(prefix + "You received §a§l" + keys + " " + keyManager.getKeyName("Random"));
                } else if (chance <= 30) {
                    int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                    keyManager.giveKeys(player, "Token", keys, false);
                    player.sendMessage(prefix + "You received §a§l" + keys + " " + keyManager.getKeyName("Token"));
                } else if (chance <= 35) {
                    int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                    keyManager.giveKeys(player, "Gamma", keys, false);
                    player.sendMessage(prefix + "You received §a§l" + keys + " " + keyManager.getKeyName("Gamma"));
                } else if (chance <= 40) {
                    int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                    keyManager.giveKeys(player, "Beta", keys, false);
                    player.sendMessage(prefix + "You received §a§l" + keys + " " + keyManager.getKeyName("Beta"));
                } else {
                    int keys = ThreadLocalRandom.current().nextInt(8) + 1;
                    keyManager.giveKeys(player, "Delta", keys, false);
                    player.sendMessage(prefix + "You received §a§l" + keys + " " + keyManager.getKeyName("Delta"));
                }*/
                if (chance <= 1) {
                    int keys = ThreadLocalRandom.current().nextInt(2) + 1;
                    keyManager.giveKeys(player, "Omega", keys, !player.hasPermission("keyfinder.remove"));
                } else if (chance <= 3) {
                    int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                    keyManager.giveKeys(player, "Token", keys, !player.hasPermission("keyfinder.remove"));
                } else if (chance <= 6) {
                    int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                    keyManager.giveKeys(player, "Gamma", keys, !player.hasPermission("keyfinder.remove"));
                } else if (chance <= 10) {
                    int keys = ThreadLocalRandom.current().nextInt(4) + 1;
                    keyManager.giveKeys(player, "Beta", keys, !player.hasPermission("keyfinder.remove"));
                } else {
                    int keys = ThreadLocalRandom.current().nextInt(8) + 1;
                    keyManager.giveKeys(player, "Delta", keys, !player.hasPermission("keyfinder.remove"));
                }

                main.removeMobCoins(player, 2500);
            }
        } else {
            player.sendMessage(prefix + "§cYou don't have enough MobCoins");
        }
    }

    public ItemStack getStickItem(int level) {
        ItemStack item = new ItemStack(Material.STICK);
        List<String> lore = new ArrayList<>();

        if (level == 1) {
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§9§lMOB §5§lCOIN §7- Collector");
            lore.add("");
            lore.add("§7Right click on a chest");
            lore.add("§7to collect all the MobCoins");
            meta.setLore(lore);
            item.setItemMeta(meta);
        } else if (level == 2) {
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§9§lMOB §5§lCOIN §2Lv2 §7- Collector");
            lore.add("");
            lore.add("§7Right click on a chest");
            lore.add("§7to collect all the MobCoins");
            lore.add("");
            lore.add("§7Multiplier: §f2x");
            meta.setLore(lore);
            item.setItemMeta(meta);
        } else if (level == 3) {
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§9§lMOB §5§lCOIN §2Lv3 §7- Collector");
            lore.add("");
            lore.add("§7Right click on a chest");
            lore.add("§7to collect all the MobCoins");
            lore.add("");
            lore.add("§7Multiplier: §f3x");
            meta.setLore(lore);
            item.setItemMeta(meta);
        } else if (level == 4) {
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§9§lMOB §5§lCOIN §2Lv4 §7- Collector");
            lore.add("");
            lore.add("§7Right click on a chest");
            lore.add("§7to collect all the MobCoins");
            lore.add("");
            lore.add("§7Multiplier: §f4x");
            meta.setLore(lore);
            item.setItemMeta(meta);
        } else if (level == 5) {
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§9§lMOB §5§lCOIN §2Lv5 §7- Collector");
            lore.add("");
            lore.add("§7Right click on a chest");
            lore.add("§7to collect all the MobCoins");
            lore.add("");
            lore.add("§7Multiplier: §f5x");
            meta.setLore(lore);
            item.setItemMeta(meta);
        }

        return item;
    }

    public void openStickUpgrades(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lMOBCOIN §f| §7Stick Upgrade");
        Main.FillBorder(gui);

        ItemStack lv2 = getStickItem(2);
        ItemMeta meta = lv2.getItemMeta();
        List<String> lore = new ArrayList<>(meta.getLore());
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e4x §fLevel 1 Collectors");
        lore.add("");
        meta.setLore(lore);
        lv2.setItemMeta(meta);

        ItemStack lv3 = getStickItem(3);
        meta = lv3.getItemMeta();
        lore = new ArrayList<>(meta.getLore());
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e4x §fLevel 2 Collectors");
        lore.add("");
        meta.setLore(lore);
        lv3.setItemMeta(meta);

        ItemStack lv4 = getStickItem(4);
        meta = lv4.getItemMeta();
        lore = new ArrayList<>(meta.getLore());
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e4x §fLevel 3 Collectors");
        lore.add("");
        meta.setLore(lore);
        lv4.setItemMeta(meta);

        ItemStack lv5 = getStickItem(5);
        meta = lv5.getItemMeta();
        lore = new ArrayList<>(meta.getLore());
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e4x §fLevel 4 Collectors");
        lore.add("");
        meta.setLore(lore);
        lv5.setItemMeta(meta);

        gui.setItem(11, lv2);
        gui.setItem(12, lv3);
        gui.setItem(14, lv4);
        gui.setItem(15, lv5);

        player.openInventory(gui);
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lMOBCOIN §f| §7Shop");
        Main.FillBorder(gui);

        ItemStack xp = new ItemStack(Material.EXP_BOTTLE);
        ItemMeta meta = xp.getItemMeta();
        meta.setDisplayName("§7Random §a§lXP");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fMobCoins");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou will get a §7Random §famount of XP");
        lore.add("§7| §ffrom §750k §fto §7100k");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        xp.setItemMeta(meta);

        ItemStack tokenPack = new ItemStack(Material.PAPER);
        meta = tokenPack.getItemMeta();
        meta.setDisplayName("§a§lTokens §7Pack");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.000 §fMobCoins");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fReceive a §7random §famount of Tokens");
        lore.add("§7| §ffrom §725k §fto §750k");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        tokenPack.setItemMeta(meta);

        ItemStack moneyPack = new ItemStack(Material.GOLD_INGOT);
        meta = moneyPack.getItemMeta();
        meta.setDisplayName("§e§lMoney §7Pack");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e1.500 §fMobCoins");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fReceive a §7random §famount of Money");
        lore.add("§7| §ffrom §7250M §fto §7500M");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        moneyPack.setItemMeta(meta);

        ItemStack moneyBoost = new ItemStack(Material.GOLD_BLOCK);
        meta = moneyBoost.getItemMeta();
        meta.setDisplayName("§e§lMONEY §7Booster §8(+5%)");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e5.000 §fMobCoins");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §fPermanent +5% Money booster (stacks)");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("");
        meta.setLore(lore);
        moneyBoost.setItemMeta(meta);

        ItemStack tokenBoost = new ItemStack(Material.MAGMA_CREAM);
        meta = tokenBoost.getItemMeta();
        meta.setDisplayName("§a§lTOKEN §7Booster §8(+2.5%)");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e5.000 §fMobCoins");
        lore.add("");
        lore.add("§7§lREWARD");
        lore.add("§7| §fPermanent +2.5% Token booster (stacks)");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("");
        meta.setLore(lore);
        tokenBoost.setItemMeta(meta);

        ItemStack keys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = keys.getItemMeta();
        meta.setDisplayName("§7Random §a§lKeys");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lPRICE");
        lore.add("§e| §e2500 §fMobCoins");
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fYou will get a §7Random Amount §fof a §7Random Key");
        lore.add("§7| §fYou can win: Delta, Beta, Gamma, Token, Omega Key");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click: §61x");
        lore.add("§6| §fRight click: §6Max Use");
        lore.add("");
        meta.setLore(lore);
        keys.setItemMeta(meta);

        ItemStack balance = new ItemStack(Material.BOOK);
        meta = balance.getItemMeta();
        meta.setDisplayName("§e§lINFORMATIONS");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lBALANCE");
        lore.add("§e| §f" + main.getMobCoins(player));
        lore.add("");
        meta.setLore(lore);
        balance.setItemMeta(meta);

        ItemStack upgrades = new ItemStack(Material.STICK);
        meta = upgrades.getItemMeta();
        meta.setDisplayName("§e§lMOBCOIN STICK §7Upgrades");
        lore = new ArrayList<>();
        lore.add("");
        lore.add("§7§lDESCRIPTION");
        lore.add("§7| §fClick me to open the Upgrades Menu");
        lore.add("");
        meta.setLore(lore);
        upgrades.setItemMeta(meta);

        gui.setItem(11, xp);
        gui.setItem(10, tokenPack);
        gui.setItem(12, moneyPack);
        gui.setItem(13, moneyBoost);
        gui.setItem(14, tokenBoost);
        gui.setItem(15, keys);
        gui.setItem(4, balance);
        gui.setItem(26, upgrades);

        player.openInventory(gui);
    }
}
