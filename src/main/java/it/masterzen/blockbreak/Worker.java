package it.masterzen.blockbreak;

import it.masterzen.Giveaway.Giveaway;
import it.masterzen.commands.Main;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import sun.security.krb5.internal.APRep;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.*;

class WorkerResume {

    private double money;
    private double tokens;
    private long keys;

    public WorkerResume() {
        money = 0;
        tokens = 0;
        keys = 0;
    }

    public void addMoney(double amount) {
        this.money = this.money + amount;
    }

    public void addTokens(double amount) {
        this.tokens = this.tokens + amount;
    }

    public void addKeys(long amount) {
        this.keys = this.keys + amount;
    }

    public double getMoney() {
        return this.money;
    }

    public double getTokens() {
        return this.tokens;
    }

    public double getKeys() {
        return this.keys;
    }

}

public class Worker implements Listener {

    private final String prefix = "§e§lWORKER §8»§7 ";
    private static YamlConfiguration ymlFile;
    private static File file;
    private static HashMap <UUID, WorkerResume> workerResume = new HashMap<>();

    public final AlphaBlockBreak mainClass;

    public Worker(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            if (event.getView().getTitle().equals("§e§lWORKER §f| §7Menu")) {
                Player player = (Player) event.getWhoClicked();
                ItemStack clickedItem = event.getCurrentItem();
                event.setCancelled(true);

                if (event.getSlot() == 22) {
                    AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
                    double playerTokens = main.getTeAPI().getTokens(player);
                    double nextLevelPrice = 0;
                    int playerLevel = getLevel(player);
                    int levelToAdd = 0;

                    //main.getLogger().info("Level: " + playerLevel);

                    if (getLevel(player) < 5000) {
                        if (event.getClick().isLeftClick()) {
                            nextLevelPrice = getLevelPrice(playerLevel, 1);
                            levelToAdd = 1;
                        } else if (event.getClick().isRightClick()) {
                            nextLevelPrice = getLevelPrice(playerLevel, 10);
                            levelToAdd = 10;
                        } else if (event.getAction().equals(InventoryAction.DROP_ONE_SLOT)) {
                            double[] data;
                            data = getMaxLevelPrice(playerLevel, playerTokens);
                            nextLevelPrice = data[0];
                            levelToAdd = (int) data[1];
                        }
                    } else {
                        player.sendMessage(prefix + "§aMax Level Reached");
                        return;
                    }

                    //main.getLogger().info("Tokens: " + playerTokens);
                    //main.getLogger().info("Price: " + nextLevelPrice);

                    if (nextLevelPrice > 0 && playerTokens > nextLevelPrice) {
                        main.getTeAPI().removeTokens(player, nextLevelPrice);
                        addLevel(player, levelToAdd, nextLevelPrice);

                        int level = getLevel(player);
                        int booster = getBooster(level);
                        List<String> Lore = getLore(booster, level);
                        ItemStack pickaxe = event.getCurrentItem();
                        ItemMeta meta = pickaxe.getItemMeta();
                        meta.setLore(Lore);
                        meta.setDisplayName("§6§lWORKER §7| Level §e" + level);
                        pickaxe.setItemMeta(meta);
                        event.getClickedInventory().setItem(22, pickaxe);
                        sendResume(player, true);
                        //openMenu(player);
                    } else {
                        player.sendMessage(prefix + "§cYou don't have enough tokens !");
                    }
                } else if (event.getSlot() == 20) {
                    giveMultiplier(player, "Token");
                } else if (event.getSlot() == 24) {
                    giveMultiplier(player, "Money");
                }
            }
        }
    }

    private void giveMultiplier(Player player, String type) {
        LuckPerms luckPerms = AlphaBlockBreak.GetInstance().getLuckPerms();
        User user = luckPerms.getPlayerAdapter(Player.class).getUser(player);
        Node multiplier = null;
        if (type.equals("Token") && !player.hasPermission("worker.token.multiplier")) {
            multiplier = Node.builder("worker.token.multiplier").expiry(Duration.ofMinutes(15)).build();
            player.sendMessage(prefix + "You received §a§l15 §7minutes of §a§lToken §7§lBooster §7§o( 1.5x )");
        } else if (type.equals("Money") && !player.hasPermission("worker.money.multiplier")) {
            multiplier = Node.builder("worker.money.multiplier").expiry(Duration.ofMinutes(15)).build();
            player.sendMessage(prefix + "You received §a§l15 §7minutes of §a§lMoney §7§lBooster §7§o( 2x )");
        }
        if (multiplier != null) {
            user.data().add(multiplier);
            luckPerms.getUserManager().saveUser(user);
        } else {
            player.sendMessage(prefix + "§cYou already have a booster activated");
        }
    }

    private void addLevel(Player player, int level, double price) throws IOException {
        file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerWorker.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        if (ymlFile.getInt(player.getUniqueId() + ".level") > 0 ) {
            ymlFile.set(player.getUniqueId() + ".level", ymlFile.getInt(player.getUniqueId() + ".level") + level);
        } else {
            ymlFile.set(player.getUniqueId() + ".level", 1 + level);
        }
        player.sendMessage(prefix + "You bought §a§l" + level + " §7Level/s for a total of §a§l" + mainClass.newFormatNumber(price) + " §7Tokens");

        ymlFile.save(file);
    }

    public int getLevel(Player player) {
        file = new File(System.getProperty("user.dir") + "/plugins/AlphaPrison/PlayerWorker.yml");
        ymlFile = YamlConfiguration.loadConfiguration(file);

        int level = 1;

        if (ymlFile.getInt(player.getUniqueId() + ".level") > 0) {
            level = ymlFile.getInt(player.getUniqueId() + ".level");
        }

        return level;
    }

    private List<String> getLore(int booster, int level) {
        List<String> Lore = new ArrayList<>();

        Lore.add("");
        Lore.add("§6§lSTATISTICS");
        Lore.add("§7§o( gains of every second )");
        if (booster > 1) {
            Lore.add("§6| §fBooster: §6" + booster + "x");
        }
        Lore.add("§6| §fTokens: §6" + mainClass.newFormatNumber(getTokens(booster, level)));
        Lore.add("§6| §fMoney: §6" + mainClass.newFormatNumber(getMoney(booster, level)));
        /*if (level <= 2500) {
            Lore.add("§6| §eDelta Keys: §6" + getDeltaKeys(booster, level));
        } else {
            Lore.add("§6| §eBeta Keys: §6" + (getDeltaKeys(booster, level) / 2));
        }*/
        //Lore.add("§3§lBETA KEYS: §b§l" + getRareKeys(booster, level));
        //Lore.add("§3§lLEGENDARY KEYS: §b§l" + getLegendaryKeys(booster, level));
        Lore.add("");
        Lore.add("§a§lLEVELS");
        if (level == 5000) {
            Lore.add("§a| §aMax Level Reached");
        } else {
            Lore.add("§a| §fLevel UP Price: §a" + mainClass.newFormatNumber(getLevelPrice(level, 1)));
        }
        Lore.add("");
        Lore.add("§6§lUSAGE");
        Lore.add("§6| §fLeft Click: §6+1 §fLevel");
        Lore.add("§6| §fRight Click: §6+10 §fLevel");
        Lore.add("§6| §fQ: §6Max Level");

        return Lore;
    }

    public double getTokens(int booster, int level) {
        return Math.round(135000L * Math.pow(1 + (0.0005 * level), 4) * booster);
    }

    public double getMoney(int booster, int level) {
        return Math.round(50000000L * Math.pow(1 + (0.0007 * level), 4) * booster);
    }

    public int getDeltaKeys(int booster, int level) {
        return (int) Math.floor(((double) level / 2500) * booster) + (level > 2500 ? 0 : 1);
    }

    //private long getRareKeys(int booster, int level) {
    //    return Math.round((level / 50) * booster) + 1;
    //}

    //private long getLegendaryKeys(int booster, int level) {
    //    return Math.round((level / 100) * booster) + 1;
    //}

    private double formula(int level) {
        if (level == 0) {
            return 35000000D;
        }
        return 17500000D * Math.pow(1 + (0.0012 * level), 4) * 5;
    }

    private double getLevelPrice(int level, int times) {
        double finalPrice;

        finalPrice = formula(level + 1);
        if (times > 1) {
            for (int i = 1; i <= times; i++) {
                //AlphaBlockBreak.GetInstance().getLogger().info(finalPrice + " i: " + i + " valore formula: " + formula(level + i));
                if (level + i < 5000) {
                    finalPrice = finalPrice + formula(level + i);
                }
            }
        }

        return Math.round(finalPrice);
    }

    private double[] getMaxLevelPrice(int level, double playerTokens) {
        int safezone = 10000;
        double[] data = new double[2];
        double finalPrice = formula(level);

        while (playerTokens >= finalPrice && safezone > 0 && level < 5000) {
            finalPrice = finalPrice + formula(level);
            level++;
            safezone--;
        }

        finalPrice = finalPrice - formula(level);

        data[0] = finalPrice;
        data[1] = 10000 - safezone;

        return data;
    }

    public void getTotalPrice(Player player, int startLevel, int endLevel) {
        double finalPrice = formula(startLevel);
        for(int i = startLevel; i <= endLevel; i++) {
            finalPrice = finalPrice + formula(i);
        }

        player.sendMessage(prefix + "Total Tokens needed: §a§l" + finalPrice);
    }

    public int getBooster(int level) {
        if (level >= 4000) {
            return 5;
        } else if (level >= 3000) {
            return 4;
        } else if (level >= 2000) {
            return 3;
        } else if (level >= 1000) {
            return 2;
        } else {
            return 1;
        }
    }

    private List<String> getBoosterLore(String type) {
        List<String> Lore = new ArrayList<>();
        if (type.equals("Token")) {
            Lore.add("");
            Lore.add("§7§lDESCRIPTION");
            Lore.add("§7| §fClick me to receive");
            Lore.add("§7| §715 §fminutes of §71.5x");
            Lore.add("§7| §fToken Multiplier");
        } else if (type.equals("Money")) {
            Lore.add("");
            Lore.add("§7§lDESCRIPTION");
            Lore.add("§7| §fClick me to receive");
            Lore.add("§7| §715 §fminutes of §72x");
            Lore.add("§7| §fMoney Multiplier");
        }
        Lore.add("");
        Lore.add("§c§lNB: §cYou can use only 1 at time");

        return Lore;
    }

    public void openMenu(Player player) {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!mainClass.getPlayersIP().containsKey(player.getUniqueId())) {
                    mainClass.loadPlayerIP(player);
                }
                if (!mainClass.getPlayerWorkerIP().contains(mainClass.getPlayersIP().getOrDefault(player.getUniqueId(), ""))) {
                    mainClass.getPlayerWorkerIP().add(mainClass.getPlayersIP().getOrDefault(player.getUniqueId(), ""));
                    Inventory gui = Bukkit.createInventory(null, 45, "§e§lWORKER §f| §7Menu");
                    Main.FillBorder(gui);

                    ItemMeta meta;
                    List<String> Lore = new ArrayList<>();
                    ItemStack pickaxe;

                    int level = getLevel(player);
                    int booster = getBooster(level);

                    if (level >= 4000) {
                        pickaxe = XMaterial.DIAMOND_PICKAXE.parseItem();
                    } else if (level >= 3000) {
                        pickaxe = XMaterial.GOLDEN_PICKAXE.parseItem();
                    } else if (level >= 2000) {
                        pickaxe = XMaterial.IRON_PICKAXE.parseItem();
                    } else if (level >= 1000) {
                        pickaxe = XMaterial.STONE_PICKAXE.parseItem();
                    } else {
                        pickaxe = XMaterial.WOODEN_PICKAXE.parseItem();
                    }

                    ItemStack tokenBoost = XMaterial.MAGMA_CREAM.parseItem();
                    meta = tokenBoost.getItemMeta();
                    meta.setDisplayName("§6§lWORKER §7| Token Booster");
                    meta.setLore(getBoosterLore("Token"));
                    tokenBoost.setItemMeta(meta);

                    ItemStack moneyBoost = XMaterial.GOLD_INGOT.parseItem();
                    meta = moneyBoost.getItemMeta();
                    meta.setDisplayName("§6§lWORKER §7| Money Booster");
                    meta.setLore(getBoosterLore("Money"));
                    moneyBoost.setItemMeta(meta);

                    meta = pickaxe.getItemMeta();
                    meta.setLore(getLore(booster, level));
                    meta.setDisplayName("§6§lWORKER §7| Level §e" + level);
                    pickaxe.setItemMeta(meta);

                    gui.setItem(20, tokenBoost);
                    gui.setItem(22, pickaxe);
                    gui.setItem(24, moneyBoost);

                    player.openInventory(gui);
                    player.sendMessage(prefix + "§aStarting farming...");
                    //startGiveRewards(player, getTokens(booster, level) * 5, getMoney(booster, level) * 5, getDeltaKeys(booster, level) * 5, getRareKeys(booster, level) * 5, getLegendaryKeys(booster, level) * 5);

                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            startGiveRewards(player, level, getTokens(booster, level) * 5, getMoney(booster, level) * 5, (getDeltaKeys(booster, level) * 5 / (level > 2500 ? 2 : 1)));
                        }
                    }.runTask(mainClass);
                } else {
                    player.sendMessage(prefix + "§cYour worker is already working for someone else §7§o(1x IP)");
                }
            }
        }.runTask(mainClass);
    }

    public void sendResume(Player player, boolean remove) {
        if (workerResume.containsKey(player.getUniqueId())) {
            int level = getLevel(player);

            player.sendMessage("§e§lWORKER RESUME");
            player.sendMessage("");
            player.sendMessage("§7+ §e" + mainClass.newFormatNumber(workerResume.get(player.getUniqueId()).getMoney()) + " §e§lMONEY");
            player.sendMessage("§7+ §a" + mainClass.newFormatNumber(workerResume.get(player.getUniqueId()).getTokens()) + " §a§lTOKENS");
            //player.sendMessage("§7+ " + mainClass.newFormatNumber(workerResume.get(player.getUniqueId()).getKeys()) + " " + (level > 2500 ? mainClass.getKeysManager().getKeyName("Beta") : mainClass.getKeysManager().getKeyName("Delta")));
            player.sendMessage("");

            if (remove) {
                workerResume.remove(player.getUniqueId());
            }
        }
    }

    public void startGiveRewards(Player player, int level, double tokens, double money, int keys) {
        AlphaBlockBreak main = AlphaBlockBreak.GetInstance();
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline() && player.getOpenInventory() != null && player.getOpenInventory().getTitle().equals("§e§lWORKER §f| §7Menu")) {
                    if (!workerResume.containsKey(player.getUniqueId())) {
                        workerResume.put(player.getUniqueId(), new WorkerResume());
                    }
                    if (player.hasPermission("worker.token.multiplier")) {
                        main.getTeAPI().addTokens(player, tokens * 1.5);
                        workerResume.get(player.getUniqueId()).addTokens(tokens * 1.5);
                    } else {
                        main.getTeAPI().addTokens(player, tokens);
                        workerResume.get(player.getUniqueId()).addTokens(tokens);
                    }
                    if (player.hasPermission("worker.money.multiplier")) {
                        main.getEconomy().depositPlayer(player, money * 1.5);
                        workerResume.get(player.getUniqueId()).addMoney(money * 1.5);
                    } else {
                        main.getEconomy().depositPlayer(player, money);
                        workerResume.get(player.getUniqueId()).addMoney(money);
                    }
                    /*if (level <= 2500) {
                        main.getKeysManager().giveKeys(player, "Delta", keys, false);
                    } else {
                        main.getKeysManager().giveKeys(player, "Beta", keys, false);
                    }*/
                    workerResume.get(player.getUniqueId()).addKeys(keys);
                } else {
                    main.removeWorker(player, true);
                    sendResume(player, true);
                    this.cancel();
                }
            }
        }.runTaskTimer(main, 100L, 100L);
    }
}
