package it.masterzen.blockbreak;

import com.earth2me.essentials.Essentials;
import net.ess3.api.MaxMoneyException;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.cacheddata.CachedMetaData;
import net.luckperms.api.model.user.User;
import net.luckperms.api.node.Node;
import net.luckperms.api.node.types.PrefixNode;
import net.luckperms.api.node.types.SuffixNode;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class Rebirth {

    private final String prefix = "§e§lREBIRTH §8»§7 ";
    public final AlphaBlockBreak mainClass;

    public Rebirth(AlphaBlockBreak plugin) {
        mainClass = plugin;
    }

    public String getPrefix() {
        return this.prefix;
    }
    private List<Player> elaboratingMaxRebirthList = new ArrayList<>();

    public void addRebirth(Player player, int currentRebirth, int rebirthToAdd) {
        LuckPerms luckPerms = AlphaBlockBreak.GetInstance().getLuckPerms();
        User user = luckPerms.getPlayerAdapter(Player.class).getUser(player);

        //PrefixNode tmpNodePrefix = PrefixNode.builder("§bLv " + (currentPrestige) + " §7", 207).build();
        //PrefixNode nodePrefix = PrefixNode.builder("§bLv 1 §7", 207).build();
        //user.data().remove(tmpNodePrefix);
        //user.data().add(nodePrefix);
        if (currentRebirth > 0) {
            SuffixNode tmpNodeSuffix = SuffixNode.builder(" §f#§l" + (currentRebirth), 207).build();
            SuffixNode nodeSuffix = SuffixNode.builder(" §f#§l" + (currentRebirth + rebirthToAdd), 207).build();
            user.data().remove(tmpNodeSuffix);
            user.data().add(nodeSuffix);
        } else {
            SuffixNode nodeSuffix = SuffixNode.builder(" §f#§l" + rebirthToAdd, 207).build();
            user.data().add(nodeSuffix);
        }
        luckPerms.getUserManager().saveUser(user);
    }

    private double formula(int rebirth) {
        //0.0012
        // 04/03/2023 from 250000000000000000D to 200000000000000000D
        return 200000000000000000D * Math.pow(1 + (0.0075 * rebirth), 4);
    }

    public boolean isPlayerExecutingRebirtMax(Player player) {
        return elaboratingMaxRebirthList.contains(player);
    }

    private void giveRebirthReward(Player player, int times) {
        LuckPerms luckPerms = AlphaBlockBreak.GetInstance().getLuckPerms();
        String tmp;
        int rebirthLevel = 0;

        User user = luckPerms.getPlayerAdapter(Player.class).getUser(player);
        CachedMetaData metaData = luckPerms.getPlayerAdapter(Player.class).getMetaData(player);
        String suffix = metaData.getSuffix();
        if (suffix != null && suffix.contains("#")) {
            tmp = suffix.replace("§f#§l", "");
            tmp = tmp.replace(" ", "");
            tmp = ChatColor.stripColor(tmp);
            rebirthLevel = Integer.parseInt(tmp);
        }

        int totalPoints = 0;
        for (int i = 0; i < times; i++) {
            if (rebirthLevel <= 4) {
                boolean rewardAdded = false;
                int reward;
                int secure = 0;

                while (!rewardAdded) {
                    if (secure < 1000) {
                        secure++;
                        reward = ThreadLocalRandom.current().nextInt(5);

                        if (player.hasPermission("rebirth.enchantfinder") && player.hasPermission("rebirth.spawnergkit") && player.hasPermission("rebirth.moneymultiplier.1") && player.hasPermission("rebirth.moneymultiplier.2") && player.hasPermission("rebirth.tokenmultiplier")) {
                            rewardAdded = true;
                            player.sendMessage(prefix + "You already received all the perks !");
                        }

                        if (reward == 0) {
                            if (!player.hasPermission("rebirth.enchantfinder")) {
                                player.sendMessage(prefix + "You unlocked the §aEnchantFinder Enchant§7");
                                player.sendMessage("§7Since now, every 250 blocks you will gain 1 free level of TokenGreed or Fortune");

                                Node rebirthPex = Node.builder("rebirth.enchantfinder").build();
                                user.data().add(rebirthPex);
                                luckPerms.getUserManager().saveUser(user);
                                rewardAdded = true;
                            }
                        } else if (reward == 1) {
                            if (!player.hasPermission("rebirth.spawnergkit")) {
                                player.sendMessage(prefix + "You unlocked the §aSpawner Gkit§7");
                                player.sendMessage("§7You can find the Gkit on §a/Gkit");

                                Node rebirthPex = Node.builder("rebirth.spawnergkit").build();
                                user.data().add(rebirthPex);
                                luckPerms.getUserManager().saveUser(user);
                                rewardAdded = true;
                            }
                        } else if (reward == 2) {
                            if (!player.hasPermission("rebirth.moneymultiplier.1")) {
                                player.sendMessage(prefix + "You unlocked the §aAutoSell Booster§7");
                                player.sendMessage("§7Since now, you will have 25% chance every block to gain REAL double Money from AutoSell");

                                Node rebirthPex = Node.builder("rebirth.moneymultiplier.1").build();
                                user.data().add(rebirthPex);
                                luckPerms.getUserManager().saveUser(user);
                                rewardAdded = true;
                            }
                        } else if (reward == 3) {
                            if (!player.hasPermission("rebirth.tokenmultiplier")) {
                                player.sendMessage(prefix + "You unlocked the §aTokenGreed Booster§7");
                                player.sendMessage("§7Since now, you will have 25% chance every block to gain REAL double Tokens from TokenGreed");

                                Node rebirthPex = Node.builder("rebirth.tokenmultiplier").build();
                                user.data().add(rebirthPex);
                                luckPerms.getUserManager().saveUser(user);
                                rewardAdded = true;
                            }
                        } else {
                            if (!player.hasPermission("rebirth.moneymultiplier.2") && player.hasPermission("rebirth.moneymultiplier.1")) {
                                player.sendMessage(prefix + "You unlocked the §aAutoSell Booster§7");
                                player.sendMessage("§7Since now, you will have 50% chance every block to gain REAL double Money from AutoSell");

                                Node rebirthPex = Node.builder("rebirth.moneymultiplier.2").build();
                                user.data().add(rebirthPex);
                                luckPerms.getUserManager().saveUser(user);
                                rewardAdded = true;
                            }
                        }
                    } else {
                        rewardAdded = true;
                    }
                }
            } else {
                totalPoints = totalPoints + ThreadLocalRandom.current().nextInt(2) + 1;
            }
            rebirthLevel++;
        }
        if (totalPoints > 0) {
            player.sendMessage(prefix + "You received §a§l" + totalPoints + " §7Rebirth point/s");
            player.sendMessage(prefix + "Spend them at §a§l/RebirthShop");

            AlphaBlockBreak.GetInstance().addRebirthPoints(player, totalPoints);
        }
    }

    public long getPrestigeNeeded(int rebirth) {
        long prestigeNeeded = 25000;

        int prestigeNeededMultiplier = rebirth / 500 + 1;
        for (int i = 1; i <= prestigeNeededMultiplier; i++) {
            prestigeNeeded = prestigeNeeded + ((i * 50L) * (rebirth >= i * 500 ? 500 : rebirth - ((i - 1) * 500L)));
        }

        return prestigeNeeded;
    }

    public void rebirth(Player player, boolean max, boolean message) throws MaxMoneyException {
        if (!elaboratingMaxRebirthList.contains(player)) {
            elaboratingMaxRebirthList.add(player);
            if (message) {
                player.sendMessage(prefix + "Please wait...");
            }

            LuckPerms luckPerms = mainClass.getLuckPerms();
            String tmp;
            long prestigeLevel = 0;
            int rebirthLevel = 0;

            CachedMetaData metaData = luckPerms.getPlayerAdapter(Player.class).getMetaData(player);
            String playerPrefix = metaData.getPrefix();
            assert playerPrefix != null;
            if (playerPrefix.contains("§bLv")) {
                tmp = playerPrefix.replace("§bLv ", "");
                tmp = tmp.replace(" ", "");
                tmp = ChatColor.stripColor(tmp);
                prestigeLevel = Long.parseLong(tmp);
            }
            playerPrefix = metaData.getSuffix();
            if (playerPrefix != null && playerPrefix.contains("#")) {
                tmp = playerPrefix.replace("§f#§l", "");
                tmp = tmp.replace(" ", "");
                tmp = ChatColor.stripColor(tmp);
                rebirthLevel = Integer.parseInt(tmp);
            }

            //long prestigeNeeded = 25000;
            long prestigeNeeded = getPrestigeNeeded(rebirthLevel);
            //int prestigeNeededMultiplier = rebirthLevel / 500 + 1;
            //for (int i = 1; i <= prestigeNeededMultiplier; i++) {
            //    prestigeNeeded = prestigeNeeded + ((i * 50L) * (rebirthLevel >= i * 500 ? 500 : rebirthLevel - ((i - 1) * 500L)));
            //}
            if (prestigeLevel >= prestigeNeeded/*(25000 + (prestigeNeededMultiplier * 50 * rebirthLevel))*/) {
                Economy economy = AlphaBlockBreak.GetInstance().getEconomy();
                Essentials essentials = AlphaBlockBreak.GetInstance().getEssentials();
                double playerMoney = economy.getBalance(player);
                double rebirthPrice = formula(rebirthLevel + 1); // 1255000000000000000000D
                //double rebirthPrice = mainClass.getPrestigeSystem().getPrice(1, prestigeNeeded); // 1255000000000000000000D
                //double rebirthPrice = mainClass.getPrestigeSystem().getPrice(1, ((rebirthLevel + 1 < 500 ? 25000 : -((prestigeNeededMultiplier - 1) * 25000L)) + ((long) prestigeNeededMultiplier * 50 * rebirthLevel))); // 1255000000000000000000D
                //double rebirthPrice = mainClass.getPrestigeSystem().getPrice(1, (((long) prestigeNeededMultiplier * 50 * rebirthLevel))); // 1255000000000000000000D

                if (!max && playerMoney > rebirthPrice && message) {
                    player.sendMessage(prefix + "§aYou can use /rebirth Max to rebirth to the max level");
                }
                if (max) {

                    //if (playerMoney >= (rebirthPrice + formula(rebirthLevel + 1))) {
                    if (playerMoney >= rebirthPrice) {
                        int rebirthsToAdd = 1;
                        int safezone = 100000;
                        //double initialMoney = playerMoney;

                        //double tmpRebirthPrice = (rebirthPrice + formula(rebirthLevel + rebirthsToAdd));

                        double tmpRebirthPrice = formula(rebirthLevel + rebirthsToAdd);
                        while (economy.has(player, tmpRebirthPrice) && prestigeLevel >= prestigeNeeded && safezone > 0) {
                            //player.sendMessage("Price for rebirth " + rebirthsToAdd + ": " + tmpRebirthPrice);
                            //player.sendMessage("Rebirth: " + rebirthLevel + " toadd: " + rebirthsToAdd + " Player money: " + playerMoney + " Price: " + tmpRebirthPrice + " prestige: " + prestigeLevel + " needed: " + prestigeNeeded);
                            //playerMoney = playerMoney - tmpRebirthPrice;
                            rebirthsToAdd++;
                            safezone--;
                            //rebirthPrice = mainClass.getPrestigeSystem().getPrice(1, (25000 + ((long) prestigeNeededMultiplier * 50 * (rebirthLevel + rebirthsToAdd))));
                            //player.sendMessage("prestige needed: " + getPrestigeNeeded(rebirthLevel + rebirthsToAdd));
                            prestigeNeeded = getPrestigeNeeded(rebirthLevel + rebirthsToAdd);
                            //rebirthPrice = mainClass.getPrestigeSystem().getPrice(1, getPrestigeNeeded(rebirthLevel + rebirthsToAdd));
                            //player.sendMessage("prestiges needed: " + (25000 + ((long) prestigeNeededMultiplier * 50 * (rebirthLevel + rebirthsToAdd))));
                            //rebirthPrice = mainClass.getPrestigeSystem().getPrice(1, (((long) prestigeNeededMultiplier * 50 * (rebirthLevel + rebirthsToAdd))));
                            //player.sendMessage("rebirthPrice: " + rebirthPrice);
                            //tmpRebirthPrice = (rebirthPrice + formula(rebirthLevel + rebirthsToAdd));
                            tmpRebirthPrice = (tmpRebirthPrice + formula(rebirthLevel + rebirthsToAdd));
                        }

                        if (!economy.has(player, tmpRebirthPrice)) {
                            tmpRebirthPrice = (tmpRebirthPrice - formula(rebirthLevel + rebirthsToAdd));
                            rebirthsToAdd--;
                        }
                        //double totalPrice = economy.getBalance(player) - playerMoney;
                        //AlphaBlockBreak.GetInstance().getEconomy().withdrawPlayer(player, totalPrice);
                        //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "eco set " + player.getName() + " " + playerMoney);
                        //essentials.getUser(player.getUniqueId()).takeMoney(BigDecimal.valueOf(tmpRebirthPrice));
                        economy.withdrawPlayer(player, tmpRebirthPrice);
                        //economy.withdrawPlayer(player, (initialMoney - playerMoney));

                        giveRebirthReward(player, rebirthsToAdd);
                        addRebirth(player, rebirthLevel, rebirthsToAdd);
                        //if (rebirthLevel > 5) {
                        //    giveRebirthReward(player, rebirthsToAdd);
                        //} else {
                        //    int i = 1;
                        //    safezone = 6;
                        //    while (rebirthLevel + i <= 5 && safezone > 0) {
                        //        giveRebirthReward(player, 1);
                        //        i++;
                        //        safezone--;
                        //    }
                        //}

                        if (message) {
                            if (essentials.getUser(player).getNickname() == null) {
                                Bukkit.broadcastMessage(essentials.getUser(player).getName() + " §7rebirth to §f#§l" + (rebirthLevel + rebirthsToAdd));
                            } else {
                                Bukkit.broadcastMessage(essentials.getUser(player).getNickname() + " §7rebirth to §f#§l" + (rebirthLevel + rebirthsToAdd));
                            }
                        }
                    } else {
                        if (message) {
                            player.sendMessage(prefix + "§cYou don't have enough money, you need §c§l" + mainClass.newFormatNumber(formula(rebirthLevel + 1)));
                        }
                    }
                } else {
                    double price = formula(rebirthLevel + 1);
                    //Bukkit.broadcastMessage("Price for rebirth " + (rebirthLevel + 1) + ": " + price);
                    if (playerMoney > price) {
                        //AlphaBlockBreak.GetInstance().getEconomy().withdrawPlayer(player, playerMoney);
                        //Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "eco take " + player.getName() + " " + price);
                        economy.withdrawPlayer(player, price);
                        giveRebirthReward(player, 1);
                        addRebirth(player, rebirthLevel, 1);

                        if (message) {
                            if (essentials.getUser(player).getNickname() == null) {
                                Bukkit.broadcastMessage(essentials.getUser(player).getName() + " §7rebirth to §f#§l" + (rebirthLevel + 1));
                            } else {
                                Bukkit.broadcastMessage(essentials.getUser(player).getNickname() + " §7rebirth to §f#§l" + (rebirthLevel + 1));
                            }
                        } else {
                            if (essentials.getUser(player).getNickname() == null) {
                                player.sendMessage(essentials.getUser(player).getName() + " §7rebirth to §f#§l" + (rebirthLevel + 1));
                            } else {
                                player.sendMessage(essentials.getUser(player).getNickname() + " §7rebirth to §f#§l" + (rebirthLevel + 1));
                            }
                        }
                    } else {
                        if (message) {
                            player.sendMessage(prefix + "§cYou don't have enough money, you need §c§l" + mainClass.newFormatNumber(price));
                        }
                    }
                }
            } else {
                if (message) {
                    player.sendMessage(prefix + "§cYou need to be at least Lv " + prestigeNeeded/*(25000 + (prestigeNeededMultiplier * 50 * rebirthLevel))*/ + " to rebirth !");
                }
            }

            elaboratingMaxRebirthList.remove(player);
        } else if (message) {
            player.sendMessage(prefix + "Already working... Please wait");
        }
    }
}
