package it.masterzen.minebomb;

import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.prestigemine.MineManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.inventory.ItemStack;

import java.lang.reflect.Method;

public class MineBombListener implements Listener {

    private final AlphaBlockBreak plugin;

    public MineBombListener(AlphaBlockBreak plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onDrop(PlayerDropItemEvent event) {
        ItemStack is = event.getItemDrop().getItemStack();
        Player player = event.getPlayer();
        if (!MineBombItem.isMineBomb(is)) return;

        // Validate player is in their own mine region
        MineManager mine = null;
        try {
            mine = plugin.getMineSystem().getMineAtLocation(player.getLocation());
        } catch (Throwable ignored) {}

        if (mine == null || mine.getOwner() == null || !mine.getOwner().equals(player.getUniqueId())) {
            player.sendMessage("§6§lMINE BOMB §8»§7 §cYou can only use this in your own mine.");
            event.setCancelled(true);
            return;
        }

        // Cancel actual drop and consume one item
        // event.setCancelled(true);
        ItemStack hand = player.getInventory().getItemInMainHand();
        if (hand != null && hand.isSimilar(is)) {
            int amt = hand.getAmount();
            if (amt <= 1) {
                player.getInventory().setItemInMainHand(null);
            } else {
                hand.setAmount(amt - 1);
            }
        } else {
            // If not in main hand, remove from cursor/drop stack
            event.getItemDrop().remove();
        }

        MineBombItem.ParsedBomb parsed = MineBombItem.parse(is);
        if (parsed == null) return;

        double blocks = Math.pow(mine.getCurrentSize(), 2D) * 36;
        if (blocks <= 0) {
            blocks = 1;
        }

        // Compute percent based on tier and size (very small and auto-scaling)
        double base = 0.0000005; // 0.0005%
        switch (parsed.tier) {
            case T2: base = 0.000001; break; // 0.001%
            case T3: base = 0.000002; break; // 0.002%
        }
        // Scale by mine blocks
        double percent = base * (Math.min(blocks, 2_000_000L));

        if (parsed.type == BombType.MONEY) {
            double bal = plugin.getEconomy().getBalance(player);
            double reward = Math.max(0, bal * percent);
            if (reward > 0) {
                plugin.getEconomy().depositPlayer(player, reward);
                plugin.getResume().addValue(player, "Money", reward);
                player.sendMessage("§6§lMINE BOMB §8»§7 §a+ " + plugin.newFormatNumber(reward) + " §7Money (§f" + plugin.newFormatNumber(percent * 100) + "%§7)");
            }
        } else {
            double tokens = plugin.getTeAPI().getTokens(player);
            double reward = Math.max(0, tokens * percent);
            if (reward > 0) {
                plugin.getTeAPI().addTokens(player, reward);
                plugin.getResume().addValue(player, "Tokens", reward);
                player.sendMessage("§6§lMINE BOMB §8»§7 §a+ " + plugin.newFormatNumber(reward) + " §7Tokens (§f" + plugin.newFormatNumber(percent * 100) + "%§7)");
            }
        }

        // Small effect
        player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.ENTITY_FIREWORK_LAUNCH, 1f, 1.2f);
        player.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION_NORMAL, player.getLocation(), 15);
    }

    private long getMineCurrentSizeSafe(MineManager mine) {
        try {
            // Try common method names reflectively to avoid hard dependency on API signature
            for (String m : new String[]{"getCurrentSize", "getCurrentBlocks", "getSize", "getBlocks"}) {
                Method method = mine.getClass().getMethod(m);
                Object res = method.invoke(mine);
                if (res instanceof Number) {
                    return ((Number) res).longValue();
                }
            }
        } catch (Throwable ignored) {}
        // Fallback: try to estimate via mineSystem if available (not reliable here); default minimal
        return 0L;
    }
}
