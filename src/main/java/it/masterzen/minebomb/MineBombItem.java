package it.masterzen.minebomb;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class MineBombItem {

    public static final String HIDDEN_TAG_PREFIX = "\u00A70MBOMB:"; // §0MBOMB: (legacy support only)

    public static ItemStack create(BombType type, BombTier tier, int amount) {
        ItemStack is = new ItemStack(Material.FIREWORK_CHARGE, Math.max(1, amount));
        ItemMeta meta = is.getItemMeta();
        String typeName = (type == BombType.MONEY ? "Money" : "Tokens");
        meta.setDisplayName("§6Mine Bomb §7(" + typeName + ") §8T" + tier.getLevel());
        List<String> lore = new ArrayList<>();
        lore.add("§7Drop this while in your mine to redeem a reward");
        lore.add("§7Reward scales with your mine size and your current " + typeName.toLowerCase());
        lore.add("");
        // Hidden row removed as per requirement: we rely on the display name now
        meta.setLore(lore);
        meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES);
        is.setItemMeta(meta);
        return is;
    }

    public static boolean isMineBomb(ItemStack is) {
        if (is == null) return false;
        if (is.getType() != Material.FIREWORK_CHARGE) return false;
        if (!is.hasItemMeta()) return false;

        ItemMeta meta = is.getItemMeta();
        // Primary detection via display name
        if (meta.hasDisplayName()) {
            String name = ChatColor.stripColor(meta.getDisplayName());
            if (name != null && name.startsWith("Mine Bomb ")) {
                // Further light validation
                if (name.contains("(") && name.contains(")") && name.contains("T")) {
                    return true;
                }
            }
        }
        // Legacy detection via hidden lore tag (keep for backward compatibility)
        if (meta.hasLore()) {
            for (String line : meta.getLore()) {
                if (ChatColor.stripColor(line).startsWith(ChatColor.stripColor(HIDDEN_TAG_PREFIX))) {
                    return true;
                }
            }
        }
        return false;
    }

    public static ParsedBomb parse(ItemStack is) {
        if (is == null || !is.hasItemMeta()) return null;
        ItemMeta meta = is.getItemMeta();

        // Prefer parsing from display name
        if (meta.hasDisplayName()) {
            ParsedBomb parsed = parseFromName(meta.getDisplayName());
            if (parsed != null) return parsed;
        }
        // Fallback to legacy hidden lore parsing
        if (meta.hasLore()) {
            for (String line : meta.getLore()) {
                String raw = ChatColor.stripColor(line);
                if (raw.startsWith(ChatColor.stripColor(HIDDEN_TAG_PREFIX))) {
                    String payload = raw.substring(ChatColor.stripColor(HIDDEN_TAG_PREFIX).length());
                    String[] parts = payload.split(":");
                    BombType type = BombType.fromString(parts[0]);
                    int tierInt = 1;
                    try { tierInt = Integer.parseInt(parts[1]); } catch (Exception ignored) {}
                    BombTier tier = BombTier.fromInt(tierInt);
                    if (type != null && tier != null) {
                        return new ParsedBomb(type, tier);
                    }
                }
            }
        }
        return null;
    }

    private static ParsedBomb parseFromName(String displayName) {
        if (displayName == null) return null;
        String name = ChatColor.stripColor(displayName).trim();
        // Expected pattern: "Mine Bomb (Money) T1" or "Mine Bomb (Tokens) T2"
        if (!name.startsWith("Mine Bomb ")) return null;
        int l = name.indexOf('(');
        int r = name.indexOf(')');
        int t = name.lastIndexOf('T');
        if (l < 0 || r < 0 || r <= l || t < 0 || t <= r) return null;
        String typeStr = name.substring(l + 1, r).trim();
        String tierStr = name.substring(t + 1).trim();
        BombType type = BombType.fromString(typeStr);
        int tierInt = 1;
        try { tierInt = Integer.parseInt(tierStr); } catch (Exception ignored) {}
        BombTier tier = BombTier.fromInt(tierInt);
        if (type == null || tier == null) return null;
        return new ParsedBomb(type, tier);
    }

    public static class ParsedBomb {
        public final BombType type;
        public final BombTier tier;
        public ParsedBomb(BombType type, BombTier tier) {
            this.type = type; this.tier = tier;
        }
    }
}
