package it.masterzen.Keys;

import com.google.common.base.CaseFormat;
import com.sk89q.worldedit.MaxChangedBlocksException;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.prestigemine.MineManager;
import it.masterzen.prestigemine.XMaterial;
import org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.LeatherArmorMeta;

import javax.swing.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class GUI implements Listener {

    private Main main;
    private KeyList keyList;

    public GUI(Main main) {
        this.main = main;
        keyList = main.getKeyList();
    }

    public String getTypeName(String name) {
        name = ChatColor.stripColor(name);
        String[] words = name.split(" ");
        StringBuilder formattedName = new StringBuilder();

        for (String part : words) {
            formattedName.append(CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, part)).append(" ");
        }
        formattedName = new StringBuilder(formattedName.toString().trim());

        return formattedName.toString();
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) throws IOException, MaxChangedBlocksException {
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(event.getView().getTopInventory())) {
            Player player = (Player) event.getWhoClicked();

            switch (event.getView().getTitle()) {
                case "§e§lKEYS §f| §7Menu": {
                    event.setCancelled(true);

                    if (event.getSlot() == 49) {
                        main.manageAutoOpenPex(player);
                        openGUI(player);
                    } else if (event.getCurrentItem() != null && event.getCurrentItem().hasItemMeta()) {
                        String type = getTypeName(event.getCurrentItem().getItemMeta().getDisplayName());

                        if (event.getClick().isRightClick()) {
                            openPreviewGUI(player, type);
                        } else if (event.getClick().isLeftClick()) {
                            int totalKeys = main.getPlayerKeys().get(player.getUniqueId()).getKeys(type);

                            if (totalKeys >= 1) {
                                main.removeKeys(player, type, 1);
                                main.openKeys(player, type, 1, true);
                                openGUI(player);
                            } else {
                                player.sendMessage(main.prefix + "§cYou don't have any key to open");
                            }
                        } else if (event.getAction().equals(InventoryAction.DROP_ONE_SLOT)) {
                            int totalKeys = main.getPlayerKeys().get(player.getUniqueId()).getKeys(type);

                            if (totalKeys >= 1) {
                                main.removeKeys(player, type, totalKeys);
                                main.openKeys(player, type, totalKeys, true);

                                openGUI(player);
                            } else {
                                player.sendMessage(main.prefix + "§cYou don't have any key to open");
                            }
                        }
                    }
                    break;
                }
                case "§e§lKEYS §f| §7Preview": {
                    event.setCancelled(true);

                    if (event.getCurrentItem() != null && event.getCurrentItem().getType().equals(Material.ARROW)) {
                        if (event.getCurrentItem().getItemMeta().getDisplayName().contains("Main Menu")) {
                            openGUI(player);
                        } else if (event.getCurrentItem().getItemMeta().getDisplayName().contains("Next Page")) {
                            openPreviewGUIOtherPage(player, "Armor");
                        }
                    }
                    break;
                }
            }
        }
    }

    public List<String> getLoreForPreview(Player player, String type) {
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§e§lBALANCE");
        lore.add("§e| §e" + main.getPlayerKeys().get(player.getUniqueId()).getKeys(type) + "§f keys");
        lore.add("");
        lore.add("§6§lUSAGE");
        lore.add("§6| §fLeft Click to open");
        lore.add("§6| §fPress Q to open all");
        lore.add("§6| §fRight click to preview");
        lore.add("");

        return lore;
    }

    public void openPreviewGUIOtherPage(Player player, String type) {
        Inventory gui = Bukkit.createInventory(null, type.equalsIgnoreCase("Armor") ? 54 : 36, "§e§lKEYS §f| §7Preview");

        List<ItemList> itemList = new ArrayList<>();
        HashMap<String, List<ItemList>> keyLists = keyList.getKeyList();
        itemList = keyLists.get(type);
        itemList = itemList.subList(52, itemList.size());

        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta meta = back.getItemMeta();
        meta.setDisplayName("§cBack to Main Menu");
        back.setItemMeta(meta);

        ItemStack next = new ItemStack(Material.ARROW);
        meta = next.getItemMeta();
        meta.setDisplayName("§7Next Page");
        next.setItemMeta(meta);

        gui.setItem(type.equalsIgnoreCase("Armor") ? 45 : 27, back);

        if (itemList.size() > 53) {
            gui.setItem(type.equalsIgnoreCase("Armor") ? 53 : 35, next);
        }

        if (!itemList.isEmpty()) {
            for (ItemList item : itemList) {
                ItemStack tmpItem = new ItemStack(item.getItem());
                if (type.equalsIgnoreCase("Armor")) {
                    it.masterzen.CustomArmor.ItemList tmpArmor = main.getMainClass().getArmorSystem().getArmorFromPex(item.getCommandToExecute().replace("givePex ", ""));
                    if (tmpArmor != null) {
                        LeatherArmorMeta tmpMeta = (LeatherArmorMeta) tmpItem.getItemMeta();
                        tmpMeta.setColor(tmpArmor.getLeatherColor());
                        tmpMeta.setDisplayName(item.getName());
                        List<String> lore = new ArrayList<>();
                        lore.addAll(item.getLore());
                        lore.add("");
                        lore.add("§7§lDESCRIPTION");
                        lore.add("§7| §7" + item.getChance() + "%§f chance to get this reward");
                        lore.add("");
                        tmpMeta.setLore(lore);
                        tmpItem.setItemMeta(tmpMeta);
                    } else {
                        main.getMainClass().getLogger().info("§c§lERROR: §ctmpArmor not found ! (openPreviewGUI) Keys GUI");
                    }
                } else {
                    ItemMeta tmpMeta = tmpItem.getItemMeta();
                    if (type.equalsIgnoreCase("Token")) {
                        double baseValue = Double.parseDouble(item.getCommandToExecute().replace("giveTokens ", ""));
                        tmpMeta.setDisplayName(item.getName() + " + §a§l" + main.getMainClass().newFormatNumber(main.getExtraValue(baseValue)));
                    } else {
                        tmpMeta.setDisplayName(item.getName());
                    }

                    List<String> lore = new ArrayList<>();
                    if (!item.getLore().isEmpty()) {
                        lore.addAll(item.getLore());
                    }
                    lore.add("");
                    lore.add("§7§lDESCRIPTION");
                    lore.add("§7| §7" + item.getChance() + "%§f chance to get this reward");
                    lore.add("");

                    tmpMeta.setLore(lore);
                    tmpItem.setItemMeta(tmpMeta);
                }
                gui.addItem(tmpItem);
            }
        }

        player.openInventory(gui);
    }

    public void openPreviewGUI(Player player, String type) {
        Inventory gui = Bukkit.createInventory(null, type.equalsIgnoreCase("Armor") ? 54 : 36, "§e§lKEYS §f| §7Preview");

        List<ItemList> itemList = new ArrayList<>();
        HashMap<String, List<ItemList>> keyLists = keyList.getKeyList();
        itemList = keyLists.get(type);

        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta meta = back.getItemMeta();
        meta.setDisplayName("§cBack to Main Menu");
        back.setItemMeta(meta);

        ItemStack next = new ItemStack(Material.ARROW);
        meta = next.getItemMeta();
        meta.setDisplayName("§7Next Page");
        next.setItemMeta(meta);

        gui.setItem(type.equalsIgnoreCase("Armor") ? 45 : 27, back);

        if (itemList.size() > 53) {
            gui.setItem(type.equalsIgnoreCase("Armor") ? 53 : 35, next);
        }

        if (!itemList.isEmpty()) {
            for (ItemList item : itemList) {
                ItemStack tmpItem = new ItemStack(item.getItem());
                if (type.equalsIgnoreCase("Armor")) {
                    it.masterzen.CustomArmor.ItemList tmpArmor = main.getMainClass().getArmorSystem().getArmorFromPex(item.getCommandToExecute().replace("givePex ", ""));
                    if (tmpArmor != null) {
                        LeatherArmorMeta tmpMeta = (LeatherArmorMeta) tmpItem.getItemMeta();
                        tmpMeta.setColor(tmpArmor.getLeatherColor());
                        tmpMeta.setDisplayName(item.getName());
                        List<String> lore = new ArrayList<>();
                        lore.addAll(item.getLore());
                        lore.add("");
                        lore.add("§7§lDESCRIPTION");
                        lore.add("§7| §7" + item.getChance() + "%§f chance to get this reward");
                        lore.add("");
                        tmpMeta.setLore(lore);
                        tmpItem.setItemMeta(tmpMeta);
                    } else {
                        main.getMainClass().getLogger().info("§c§lERROR: §ctmpArmor not found ! (openPreviewGUI) Keys GUI");
                    }
                } else {
                    ItemMeta tmpMeta = tmpItem.getItemMeta();
                    //if (type.equalsIgnoreCase("Token")) {
                    if (item.getCommandToExecute().contains("giveTokens")) {
                        double baseValue = Double.parseDouble(item.getCommandToExecute().replace("giveTokens ", ""));
                        tmpMeta.setDisplayName(item.getName() + " + §a§l" + main.getMainClass().newFormatNumber(main.getExtraValue(baseValue)));
                    } else {
                        tmpMeta.setDisplayName(item.getName());
                    }

                    List<String> lore = new ArrayList<>();
                    if (!item.getLore().isEmpty()) {
                        lore.addAll(item.getLore());
                    }
                    lore.add("");
                    lore.add("§7§lDESCRIPTION");
                    lore.add("§7| §7" + item.getChance() + "%§f chance to get this reward");
                    lore.add("");

                    tmpMeta.setLore(lore);
                    tmpItem.setItemMeta(tmpMeta);
                }
                gui.addItem(tmpItem);
            }
        }

        player.openInventory(gui);
    }

    public void openGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§e§lKEYS §f| §7Menu");
        //main.getMainClass().FillBorder(gui);
        List<Integer> whiteGlass = getIndexForColor("white");
        List<Integer> limeGlass = getIndexForColor("lime");
        List<Integer> yellowGlass = getIndexForColor("yellow");
        List<Integer> orangeGlass = getIndexForColor("orange");
        List<Integer> redGlass = getIndexForColor("red");

        ItemStack deltaKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        ItemMeta meta = deltaKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Delta"));
        meta.setLore(getLoreForPreview(player, "Delta"));
        main.getMainClass().addGlowing(meta);
        deltaKeys.setItemMeta(meta);

        ItemStack betaKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = betaKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Beta"));
        meta.setLore(getLoreForPreview(player, "Beta"));
        main.getMainClass().addGlowing(meta);
        betaKeys.setItemMeta(meta);

        ItemStack gammaKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = gammaKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Gamma"));
        meta.setLore(getLoreForPreview(player, "Gamma"));
        main.getMainClass().addGlowing(meta);
        gammaKeys.setItemMeta(meta);

        ItemStack omegaKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = omegaKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Omega"));
        meta.setLore(getLoreForPreview(player, "Omega"));
        main.getMainClass().addGlowing(meta);
        omegaKeys.setItemMeta(meta);

        ItemStack alphaKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = alphaKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Alpha"));
        meta.setLore(getLoreForPreview(player, "Alpha"));
        main.getMainClass().addGlowing(meta);
        alphaKeys.setItemMeta(meta);

        ItemStack tokenKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = tokenKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Token"));
        meta.setLore(getLoreForPreview(player, "Token"));
        main.getMainClass().addGlowing(meta);
        tokenKeys.setItemMeta(meta);

        ItemStack randomKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = randomKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Random"));
        meta.setLore(getLoreForPreview(player, "Random"));
        main.getMainClass().addGlowing(meta);
        randomKeys.setItemMeta(meta);

        ItemStack voteKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = voteKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Vote"));
        meta.setLore(getLoreForPreview(player, "Vote"));
        main.getMainClass().addGlowing(meta);
        voteKeys.setItemMeta(meta);

        ItemStack armorKeys = new ItemStack(Material.TRIPWIRE_HOOK);
        meta = armorKeys.getItemMeta();
        meta.setDisplayName(main.getKeyName("Armor"));
        meta.setLore(getLoreForPreview(player, "Armor"));
        main.getMainClass().addGlowing(meta);
        armorKeys.setItemMeta(meta);

        gui.setItem(13, deltaKeys);
        gui.setItem(21, betaKeys);
        gui.setItem(23, gammaKeys);
        gui.setItem(29, tokenKeys);
        gui.setItem(31, voteKeys);
        gui.setItem(33, randomKeys);
        gui.setItem(39, armorKeys);
        gui.setItem(40, omegaKeys);
        gui.setItem(41, alphaKeys);

        for (int index : whiteGlass) {
            gui.setItem(index, XMaterial.WHITE_STAINED_GLASS_PANE.parseItem());
        }
        for (int index : limeGlass) {
            gui.setItem(index, XMaterial.LIME_STAINED_GLASS_PANE.parseItem());
        }
        for (int index : yellowGlass) {
            gui.setItem(index, XMaterial.YELLOW_STAINED_GLASS_PANE.parseItem());
        }
        for (int index : orangeGlass) {
            gui.setItem(index, XMaterial.ORANGE_STAINED_GLASS_PANE.parseItem());
        }
        for (int index : redGlass) {
            gui.setItem(index, XMaterial.RED_STAINED_GLASS_PANE.parseItem());
        }

        ItemStack autoOpen = new ItemStack(Material.DIAMOND);
        meta = autoOpen.getItemMeta();
        meta.setDisplayName("§6§lAUTO §e§lOPEN §7perk");
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Automatically opens all obtained keys");
        lore.add("");
        if (player.hasPermission("essentials.kits.mvp+")) {
            if (player.hasPermission("keys.autoopen")) {
                lore.add("§7Status: §a§lENABLED");
                lore.add("");
                lore.add("§7Click me to Disable");
            } else {
                lore.add("§7Status: §c§lDISABLED");
                lore.add("");
                lore.add("§7Click me to Enable");
            }
            main.getMainClass().addGlowing(meta);
        } else {
            lore.add("§cTo unlock this perk");
            lore.add("§cYou need to be at least §6§lMVP+");
            lore.add("");
            lore.add("§7Get your VIP on /buy");
        }
        meta.setLore(lore);
        autoOpen.setItemMeta(meta);

        gui.setItem(49, autoOpen);

        player.openInventory(gui);
    }

    public List<Integer> getIndexForColor(String color) {
        List<Integer> indexes = new ArrayList<>();

        switch (color) {
            case "white":
                indexes.add(0);
                indexes.add(1);
                indexes.add(2);
                indexes.add(3);
                indexes.add(4);
                indexes.add(5);
                indexes.add(6);
                indexes.add(7);
                indexes.add(8);
                indexes.add(9);
                indexes.add(17);
                indexes.add(18);
                indexes.add(26);
                indexes.add(27);
                indexes.add(35);
                indexes.add(36);
                indexes.add(44);
                indexes.add(45);
                indexes.add(46);
                indexes.add(47);
                indexes.add(48);
                indexes.add(49);
                indexes.add(50);
                indexes.add(51);
                indexes.add(52);
                indexes.add(53);
                break;
            case "lime":
                indexes.add(10);
                indexes.add(11);
                indexes.add(12);
                indexes.add(14);
                indexes.add(15);
                indexes.add(16);
                break;
            case "yellow":
                indexes.add(19);
                indexes.add(20);
                indexes.add(22);
                indexes.add(24);
                indexes.add(25);
                break;
            case "orange":
                indexes.add(28);
                indexes.add(30);
                indexes.add(32);
                indexes.add(34);
                break;
            case "red":
                indexes.add(37);
                indexes.add(38);
                //indexes.add(40);
                indexes.add(42);
                indexes.add(43);
                break;
        }

        return indexes;
    }
}
